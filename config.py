#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
普通话测评系统配置文件
"""

import os
from pathlib import Path

class Config:
    """系统配置类"""
    
    # 数据路径配置
    DATA_DIR = Path(".")
    AUDIO_DIR = DATA_DIR / "分开"
    TEXT_FILE = DATA_DIR / "50篇--朗读短文.docx(1).txt"
    
    # 音频处理参数
    SAMPLE_RATE = 16000
    HOP_LENGTH = 512
    N_MFCC = 13
    
    # Wav2Vec2模型配置
    # WAV2VEC2_MODEL = "facebook/wav2vec2-base"
    # 如果需要中文优化版本，可以使用:
    WAV2VEC2_MODEL = "facebook/wav2vec2-large-xlsr-53"
    
    # 评测权重配置
    EVALUATION_WEIGHTS = {
        'initials': 0.25,    # 声母权重 25%
        'finals': 0.30,      # 韵母权重 30%
        'tones': 0.25,       # 声调权重 25%
        'fluency': 0.20      # 流畅性权重 20%
    }
    
    # 声母列表 (21个)
    INITIALS = [
        'b', 'p', 'm', 'f',           # 唇音
        'd', 't', 'n', 'l',           # 舌尖音
        'g', 'k', 'h',                # 舌根音
        'j', 'q', 'x',                # 舌面音
        'zh', 'ch', 'sh', 'r',        # 翘舌音
        'z', 'c', 's'                 # 平舌音
    ]
    
    # 韵母列表 (39个)
    FINALS = [
        # 单韵母
        'a', 'o', 'e', 'i', 'u', 'v',
        # 复韵母
        'ai', 'ei', 'ui', 'ao', 'ou', 'iu', 'ie', 've', 'er',
        # 鼻韵母
        'an', 'en', 'in', 'un', 'vn', 'ang', 'eng', 'ing', 'ong',
        # 齐齿呼
        'ia', 'iao', 'iou', 'ian', 'iang', 'iong',
        # 合口呼
        'ua', 'uai', 'uan', 'uang', 'ue', 'ueng',
        # 撮口呼
        'van', 'vang'
    ]
    
    # 声调标准F0模板 (归一化)
    TONE_TEMPLATES = {
        1: [0.0, 0.1, 0.2, 0.3, 0.4],        # 一声：平调 55
        2: [0.0, 0.3, 0.6, 0.8, 1.0],        # 二声：升调 35
        3: [0.0, -0.2, -0.4, -0.2, 0.2],     # 三声：曲折调 214
        4: [1.0, 0.7, 0.4, 0.1, 0.0]         # 四声：降调 51
    }
    
    # 流畅性评测参数
    FLUENCY_CONFIG = {
        'ideal_speech_rate_range': (3.0, 6.0),    # 理想语速范围 (音节/秒)
        'ideal_pause_range': (0.1, 0.5),          # 理想停顿时长范围 (秒)
        'long_pause_threshold': 1.0,               # 过长停顿阈值 (秒)
        'rms_percentile_threshold': 30             # RMS能量阈值百分位
    }
    
    # 评分阈值配置
    SCORE_THRESHOLDS = {
        'excellent': 90,     # 优秀
        'good': 80,          # 良好
        'fair': 70,          # 一般
        'poor': 60           # 较差
    }
    
    # 输出配置
    OUTPUT_CONFIG = {
        'results_file': 'evaluation_results.json',
        'detailed_report': True,
        'save_features': False,  # 是否保存提取的特征
        'save_alignments': False  # 是否保存对齐结果
    }
    
    # 日志配置
    LOGGING_CONFIG = {
        'level': 'INFO',
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'file': 'putonghua_evaluation.log'
    }
    
    # 模型训练配置 (如果需要训练自定义模型)
    TRAINING_CONFIG = {
        'batch_size': 16,
        'learning_rate': 1e-4,
        'num_epochs': 50,
        'validation_split': 0.2,
        'early_stopping_patience': 10
    }
    
    # 数据增强配置
    AUGMENTATION_CONFIG = {
        'time_stretch_range': (0.8, 1.2),    # 时间拉伸范围
        'pitch_shift_range': (-2, 2),        # 音调变化范围 (半音)
        'noise_factor': 0.005,                # 噪声强度
        'volume_range': (0.7, 1.3)           # 音量变化范围
    }
    
    @classmethod
    def validate_config(cls):
        """验证配置有效性"""
        # 检查数据路径
        if not cls.AUDIO_DIR.exists():
            raise FileNotFoundError(f"音频目录不存在: {cls.AUDIO_DIR}")
        
        if not cls.TEXT_FILE.exists():
            raise FileNotFoundError(f"文本文件不存在: {cls.TEXT_FILE}")
        
        # 检查权重总和
        weight_sum = sum(cls.EVALUATION_WEIGHTS.values())
        if abs(weight_sum - 1.0) > 1e-6:
            raise ValueError(f"评测权重总和应为1.0，当前为: {weight_sum}")
        
        print("配置验证通过")
    
    @classmethod
    def get_model_config(cls):
        """获取模型配置"""
        return {
            'sample_rate': cls.SAMPLE_RATE,
            'hop_length': cls.HOP_LENGTH,
            'n_mfcc': cls.N_MFCC,
            'wav2vec2_model': cls.WAV2VEC2_MODEL,
            'weights': cls.EVALUATION_WEIGHTS
        }
    
    @classmethod
    def get_evaluation_config(cls):
        """获取评测配置"""
        return {
            'initials': cls.INITIALS,
            'finals': cls.FINALS,
            'tone_templates': cls.TONE_TEMPLATES,
            'fluency_config': cls.FLUENCY_CONFIG,
            'score_thresholds': cls.SCORE_THRESHOLDS
        }

# 全局配置实例
config = Config()

if __name__ == "__main__":
    # 验证配置
    try:
        Config.validate_config()
        print("配置文件加载成功")
        
        # 打印主要配置信息
        print(f"数据目录: {Config.DATA_DIR}")
        print(f"音频目录: {Config.AUDIO_DIR}")
        print(f"采样率: {Config.SAMPLE_RATE}Hz")
        print(f"评测权重: {Config.EVALUATION_WEIGHTS}")
        
    except Exception as e:
        print(f"配置验证失败: {e}")
