#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
普通话测评系统演示脚本
"""

import sys
import json
import time
from pathlib import Path
from putonghua_hybrid_system import PutonghuaHybridSystem, EvaluationResult
from config import Config

def print_separator(title=""):
    """打印分隔线"""
    print("=" * 60)
    if title:
        print(f" {title} ".center(60, "="))
        print("=" * 60)

def print_evaluation_result(audio_id: int, result: EvaluationResult):
    """打印评测结果"""
    print(f"\n📊 音频 {audio_id} 评测结果:")
    print("-" * 40)
    print(f"🗣️  声母评分: {result.initials_score:.1f}/100")
    print(f"🎵  韵母评分: {result.finals_score:.1f}/100")
    print(f"📈  声调评分: {result.tones_score:.1f}/100")
    print(f"⚡  流畅性评分: {result.fluency_score:.1f}/100")
    print("-" * 40)
    print(f"🏆  总分: {result.overall_score:.1f}/100")
    
    # 评级
    if result.overall_score >= 90:
        grade = "优秀 ⭐⭐⭐⭐⭐"
    elif result.overall_score >= 80:
        grade = "良好 ⭐⭐⭐⭐"
    elif result.overall_score >= 70:
        grade = "一般 ⭐⭐⭐"
    elif result.overall_score >= 60:
        grade = "及格 ⭐⭐"
    else:
        grade = "需要改进 ⭐"
    
    print(f"📝  评级: {grade}")
    
    # 显示建议
    if 'suggestions' in result.detailed_feedback and result.detailed_feedback['suggestions']:
        print("\n💡 改进建议:")
        for i, suggestion in enumerate(result.detailed_feedback['suggestions'], 1):
            print(f"   {i}. {suggestion}")

def demo_single_evaluation():
    """演示单个音频评测"""
    print_separator("单个音频评测演示")
    
    try:
        # 初始化系统
        print("🚀 正在初始化普通话测评系统...")
        system = PutonghuaHybridSystem()
        print("✅ 系统初始化完成")
        
        # 选择要评测的音频
        audio_id = 1
        print(f"\n🎤 正在评测音频 {audio_id}...")
        
        start_time = time.time()
        result = system.evaluate_audio(audio_id)
        end_time = time.time()
        
        print(f"⏱️  评测耗时: {end_time - start_time:.2f}秒")
        
        # 显示结果
        print_evaluation_result(audio_id, result)
        
        return result
        
    except Exception as e:
        print(f"❌ 评测失败: {e}")
        return None

def demo_batch_evaluation():
    """演示批量评测"""
    print_separator("批量评测演示")
    
    try:
        # 初始化系统
        print("🚀 正在初始化普通话测评系统...")
        system = PutonghuaHybridSystem()
        print("✅ 系统初始化完成")
        
        # 批量评测前5个音频
        audio_ids = [1, 2, 3, 4, 5]
        print(f"\n🎤 正在批量评测音频: {audio_ids}")
        
        start_time = time.time()
        results = system.batch_evaluate(audio_ids)
        end_time = time.time()
        
        print(f"⏱️  批量评测耗时: {end_time - start_time:.2f}秒")
        
        # 显示结果统计
        print("\n📈 批量评测结果统计:")
        print("-" * 60)
        print(f"{'音频ID':<8} {'声母':<8} {'韵母':<8} {'声调':<8} {'流畅性':<8} {'总分':<8}")
        print("-" * 60)
        
        total_scores = []
        for audio_id, result in results.items():
            print(f"{audio_id:<8} {result.initials_score:<8.1f} {result.finals_score:<8.1f} "
                  f"{result.tones_score:<8.1f} {result.fluency_score:<8.1f} {result.overall_score:<8.1f}")
            total_scores.append(result.overall_score)
        
        print("-" * 60)
        avg_score = sum(total_scores) / len(total_scores)
        print(f"平均分: {avg_score:.1f}")
        print(f"最高分: {max(total_scores):.1f}")
        print(f"最低分: {min(total_scores):.1f}")
        
        # 保存结果
        output_file = "demo_results.json"
        system.save_results(results, output_file)
        print(f"\n💾 结果已保存到: {output_file}")
        
        return results
        
    except Exception as e:
        print(f"❌ 批量评测失败: {e}")
        return None

def demo_system_info():
    """显示系统信息"""
    print_separator("系统信息")
    
    print("🔧 系统配置:")
    print(f"   数据目录: {Config.DATA_DIR}")
    print(f"   音频目录: {Config.AUDIO_DIR}")
    print(f"   文本文件: {Config.TEXT_FILE}")
    print(f"   采样率: {Config.SAMPLE_RATE}Hz")
    print(f"   Wav2Vec2模型: {Config.WAV2VEC2_MODEL}")
    
    print("\n⚖️  评测权重:")
    for dimension, weight in Config.EVALUATION_WEIGHTS.items():
        print(f"   {dimension}: {weight*100:.0f}%")
    
    print("\n🎯 评测维度:")
    print(f"   声母数量: {len(Config.INITIALS)}")
    print(f"   韵母数量: {len(Config.FINALS)}")
    print(f"   声调数量: {len(Config.TONE_TEMPLATES)}")
    
    # 检查数据文件
    print("\n📁 数据文件检查:")
    if Config.AUDIO_DIR.exists():
        audio_files = list(Config.AUDIO_DIR.glob("*.MP3"))
        print(f"   音频文件数量: {len(audio_files)}")
    else:
        print("   ❌ 音频目录不存在")
    
    if Config.TEXT_FILE.exists():
        print(f"   ✅ 文本文件存在")
    else:
        print("   ❌ 文本文件不存在")

def interactive_demo():
    """交互式演示"""
    print_separator("普通话测评系统交互演示")
    
    while True:
        print("\n请选择操作:")
        print("1. 单个音频评测")
        print("2. 批量音频评测")
        print("3. 系统信息")
        print("4. 退出")
        
        try:
            choice = input("\n请输入选项 (1-4): ").strip()
            
            if choice == '1':
                audio_id = input("请输入音频编号 (1-50): ").strip()
                try:
                    audio_id = int(audio_id)
                    if 1 <= audio_id <= 50:
                        system = PutonghuaHybridSystem()
                        result = system.evaluate_audio(audio_id)
                        print_evaluation_result(audio_id, result)
                    else:
                        print("❌ 音频编号应在1-50之间")
                except ValueError:
                    print("❌ 请输入有效的数字")
            
            elif choice == '2':
                demo_batch_evaluation()
            
            elif choice == '3':
                demo_system_info()
            
            elif choice == '4':
                print("👋 感谢使用普通话测评系统!")
                break
            
            else:
                print("❌ 无效选项，请重新选择")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已退出")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")

def main():
    """主函数"""
    print("🎙️  普通话测评系统演示")
    print("基于Wav2Vec2特征提取 + 传统评测算法的混合方案")
    
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
        
        if mode == 'single':
            demo_single_evaluation()
        elif mode == 'batch':
            demo_batch_evaluation()
        elif mode == 'info':
            demo_system_info()
        else:
            print(f"❌ 未知模式: {mode}")
            print("可用模式: single, batch, info")
    else:
        # 交互式模式
        interactive_demo()

if __name__ == "__main__":
    main()
