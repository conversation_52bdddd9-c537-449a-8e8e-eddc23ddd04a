# 普通话测评系统

基于标准音频训练的普通话发音评测系统，使用Wav2Vec2特征提取和传统信号处理算法的混合方案。

## 🎯 核心理念

**您的50篇音频是标准参考** → **训练评测模型** → **评测其他音频**

- 📚 **标准音频训练**: 基于您的50篇标准朗读音频建立评测基准
- 🎯 **四维度评测**: 声母、韵母、声调、流畅性全面评估
- 🔬 **科学对比**: 通过与标准音频特征对比给出客观评分
- ⚡ **高效评测**: 单篇音频处理时间 < 30秒
- 📊 **详细反馈**: 提供具体的改进建议和统计分析

## 🚀 正确使用流程

### 第一步: 构建标准模板 (首次使用)
```bash
# 基于您的50篇标准音频构建评测模板
python train_and_test_demo.py build
```

### 第二步: 评测待测音频 (日常使用)
```bash
# 使用构建好的模板评测音频
python train_and_test_demo.py test
```

## 📊 评测维度

| 维度 | 权重 | 评测内容 |
|------|------|----------|
| 声母 | 25% | 21个声母的发音准确性 |
| 韵母 | 30% | 39个韵母的发音准确性 |
| 声调 | 25% | 四声调的音调曲线匹配 |
| 流畅性 | 20% | 语速、停顿、连贯性评估 |

## 🛠️ 技术架构

```
普通话测评系统
├── 特征提取层 (Wav2Vec2)
│   ├── 预训练语音表示学习
│   └── 768维特征向量提取
├── 传统评测算法
│   ├── DTW序列对齐
│   ├── GMM韵母建模
│   └── F0声调分析
└── 多维度评分融合
    ├── 加权平均
    └── 详细反馈生成
```

## 📦 安装依赖

```bash
# 安装基础依赖
pip install -r requirements.txt

# 如果需要使用Wav2Vec2 (可选)
pip install torch transformers
```

## 🚀 快速开始

### 1. 数据准备

确保您的数据结构如下：
```
项目目录/
├── 分开/                    # 标准音频文件目录
│   ├── 01.MP3              # 标准音频文件 (编号01-50)
│   ├── 02.MP3
│   └── ...
├── 50篇--朗读短文.docx(1).txt  # 对应的文本内容
└── putonghua_hybrid_system.py  # 主程序
```

### 2. 首次使用 - 构建标准模板

```python
from putonghua_hybrid_system import PutonghuaHybridSystem

# 初始化系统 (不加载现有模板)
system = PutonghuaHybridSystem(load_standard_templates=False)

# 基于50篇标准音频构建评测模板
system.build_standard_templates()  # 这一步很重要!

print("标准模板构建完成，已保存到 standard_templates.json")
```

### 3. 日常使用 - 评测待测音频

```python
# 初始化系统 (加载已构建的模板)
system = PutonghuaHybridSystem(load_standard_templates=True)

# 方式1: 评测标准音频本身 (验证系统)
result = system.evaluate_audio(1)
print(f"总分: {result.overall_score:.1f}")

# 方式2: 评测外部音频文件 (实际应用)
result = system.evaluate_test_audio(
    test_audio_path="user_audio.wav",
    reference_text="用户朗读的对应文本内容"
)
print(f"用户音频评分: {result.overall_score:.1f}")
```

### 4. 运行演示

```bash
# 完整的训练和测试流程
python train_and_test_demo.py

# 直接构建模板
python train_and_test_demo.py build

# 直接测试评测
python train_and_test_demo.py test
```

## 📋 配置说明

主要配置项在 `config.py` 中：

```python
# 评测权重配置
EVALUATION_WEIGHTS = {
    'initials': 0.25,    # 声母权重
    'finals': 0.30,      # 韵母权重
    'tones': 0.25,       # 声调权重
    'fluency': 0.20      # 流畅性权重
}

# 音频处理参数
SAMPLE_RATE = 16000      # 采样率
HOP_LENGTH = 512         # 帧移
N_MFCC = 13             # MFCC维度

# Wav2Vec2模型
WAV2VEC2_MODEL = "facebook/wav2vec2-base"
```

## 🎯 评分标准

- **90-100分**: 优秀 ⭐⭐⭐⭐⭐
- **80-89分**: 良好 ⭐⭐⭐⭐
- **70-79分**: 一般 ⭐⭐⭐
- **60-69分**: 及格 ⭐⭐
- **60分以下**: 需要改进 ⭐

## 📈 算法详解

### 声母评测
- 提取音节起始30%的MFCC特征
- 与21个标准声母模板进行DTW对齐
- 基于相似度计算准确性评分

### 韵母评测
- 分析音节中段40%-90%的共振峰特征
- 使用GMM建模39个韵母的特征空间
- 计算发音偏离度评分

### 声调评测
- 提取基频F0轨迹并归一化
- 与四声调标准模板进行相关性分析
- 基于皮尔逊相关系数评分

### 流畅性评测
- 语音活动检测和停顿分析
- 语速计算 (音节/秒)
- 音量稳定性评估
- 综合流畅度指标

## 🔧 自定义扩展

### 添加新的评测维度

```python
def _evaluate_custom_dimension(self, audio, features, units):
    """自定义评测维度"""
    # 实现您的评测逻辑
    scores = []
    for unit in units:
        # 计算单个音节的评分
        score = your_evaluation_function(unit)
        scores.append(score)
    
    return np.mean(scores) * 100
```

### 修改评测权重

在 `config.py` 中修改 `EVALUATION_WEIGHTS` 字典。

### 使用不同的预训练模型

```python
# 在config.py中修改
WAV2VEC2_MODEL = "facebook/wav2vec2-large-xlsr-53"  # 多语言版本
```

## 📊 输出格式

评测结果以JSON格式保存：

```json
{
  "1": {
    "scores": {
      "initials": 85.2,
      "finals": 87.8,
      "tones": 82.1,
      "fluency": 89.3,
      "overall": 86.1
    },
    "feedback": {
      "suggestions": [
        "注意平翘舌音的区别",
        "加强声调练习"
      ],
      "statistics": {
        "total_syllables": 156,
        "initial_distribution": {...},
        "final_distribution": {...}
      }
    }
  }
}
```

## 🐛 常见问题

### Q: 提示找不到音频文件
A: 检查音频文件是否在 `分开` 目录中，文件名格式为 `01.MP3`, `02.MP3` 等。

### Q: Wav2Vec2模型加载失败
A: 系统会自动降级到传统MFCC特征，不影响基本功能。如需使用Wav2Vec2，请确保安装了 `torch` 和 `transformers`。

### Q: 评测速度较慢
A: 可以在 `config.py` 中禁用Wav2Vec2，使用传统特征提取方法。

### Q: 如何提高评测准确性
A: 
1. 使用更多标准音频建立模板
2. 调整各维度的评测权重
3. 优化特征提取参数

## 📄 许可证

本项目仅供学习和研究使用。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进系统。

## 📞 联系方式

如有问题或建议，请通过Issue联系。
