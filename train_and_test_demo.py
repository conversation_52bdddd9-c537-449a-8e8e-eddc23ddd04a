#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
普通话测评系统 - 训练和测试演示
正确的使用流程：
1. 基于50篇标准音频构建评测模板
2. 使用模板评测其他待测音频
"""

import sys
import time
from pathlib import Path
from putonghua_hybrid_system import PutonghuaHybridSystem, EvaluationResult

def print_separator(title=""):
    """打印分隔线"""
    print("=" * 70)
    if title:
        print(f" {title} ".center(70, "="))
        print("=" * 70)

def step1_build_templates():
    """步骤1: 基于标准音频构建评测模板"""
    print_separator("步骤1: 构建标准评测模板")
    
    print("📚 这一步将基于您的50篇标准音频构建评测模板")
    print("   - 分析每个声母、韵母、声调的标准特征")
    print("   - 建立流畅性基准")
    print("   - 保存模板供后续评测使用")
    
    try:
        # 初始化系统 (不加载现有模板)
        print("\n🚀 正在初始化系统...")
        system = PutonghuaHybridSystem(load_standard_templates=False)
        
        # 构建标准模板 (使用前10篇作为示例，实际应使用全部50篇)
        print("\n🔨 正在构建标准模板...")
        print("   注意: 这个过程可能需要几分钟时间")
        
        start_time = time.time()
        
        # 可以选择使用部分音频进行快速测试
        # system.build_standard_templates([1, 2, 3, 4, 5])  # 快速测试
        system.build_standard_templates()  # 使用全部50篇
        
        end_time = time.time()
        
        print(f"✅ 标准模板构建完成! 耗时: {end_time - start_time:.1f}秒")
        print(f"📁 模板已保存到: standard_templates.json")
        
        # 显示模板统计信息
        print("\n📊 模板统计信息:")
        print(f"   声母模板数量: {len(system.standard_templates['initials'])}")
        print(f"   韵母模板数量: {len(system.standard_templates['finals'])}")
        print(f"   声调模板数量: {len(system.standard_templates['tones'])}")
        
        if system.standard_templates['initials']:
            print(f"   声母类型: {list(system.standard_templates['initials'].keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模板构建失败: {e}")
        return False

def step2_test_with_templates():
    """步骤2: 使用模板评测待测音频"""
    print_separator("步骤2: 使用标准模板评测音频")
    
    print("🎯 这一步将使用构建好的标准模板评测音频")
    print("   - 加载标准模板")
    print("   - 与标准特征对比")
    print("   - 给出准确的评分")
    
    try:
        # 检查模板文件是否存在
        templates_file = Path("standard_templates.json")
        if not templates_file.exists():
            print("❌ 标准模板文件不存在，请先运行步骤1构建模板")
            return False
        
        # 初始化系统 (加载现有模板)
        print("\n🚀 正在加载标准模板...")
        system = PutonghuaHybridSystem(load_standard_templates=True)
        
        # 方式1: 评测标准音频本身 (应该得到高分)
        print("\n🧪 测试1: 评测标准音频本身")
        test_audio_id = 1
        result = system.evaluate_audio(test_audio_id)
        
        print(f"📊 音频 {test_audio_id} 评测结果 (标准音频):")
        print(f"   🗣️  声母: {result.initials_score:.1f}/100")
        print(f"   🎵  韵母: {result.finals_score:.1f}/100")
        print(f"   📈  声调: {result.tones_score:.1f}/100")
        print(f"   ⚡  流畅性: {result.fluency_score:.1f}/100")
        print(f"   🏆  总分: {result.overall_score:.1f}/100")
        
        # 方式2: 评测外部音频文件 (如果有的话)
        print("\n🧪 测试2: 评测外部音频文件")
        print("   如果您有待测的音频文件，可以使用以下方式:")
        print("   system.evaluate_test_audio('path/to/test.wav', '对应的文本内容')")
        
        # 示例代码 (注释掉，因为没有实际的测试文件)
        """
        test_audio_path = "test_audio.wav"  # 您的测试音频路径
        reference_text = "这是对应的参考文本"  # 对应的文本内容
        
        if Path(test_audio_path).exists():
            result = system.evaluate_test_audio(test_audio_path, reference_text)
            print(f"外部音频评测结果: {result.overall_score:.1f}/100")
        """
        
        return True
        
    except Exception as e:
        print(f"❌ 评测失败: {e}")
        return False

def step3_batch_comparison():
    """步骤3: 批量对比测试"""
    print_separator("步骤3: 批量对比测试")
    
    print("📈 批量测试多个标准音频，观察评分分布")
    
    try:
        system = PutonghuaHybridSystem(load_standard_templates=True)
        
        # 测试前5个音频
        test_ids = [1, 2, 3, 4, 5]
        print(f"\n🎤 正在批量评测音频: {test_ids}")
        
        results = {}
        for audio_id in test_ids:
            print(f"   正在评测音频 {audio_id}...")
            result = system.evaluate_audio(audio_id)
            results[audio_id] = result
        
        # 显示对比结果
        print("\n📊 批量评测结果对比:")
        print("-" * 70)
        print(f"{'音频ID':<8} {'声母':<8} {'韵母':<8} {'声调':<8} {'流畅性':<10} {'总分':<8}")
        print("-" * 70)
        
        total_scores = []
        for audio_id, result in results.items():
            print(f"{audio_id:<8} {result.initials_score:<8.1f} {result.finals_score:<8.1f} "
                  f"{result.tones_score:<8.1f} {result.fluency_score:<10.1f} {result.overall_score:<8.1f}")
            total_scores.append(result.overall_score)
        
        print("-" * 70)
        print(f"平均分: {sum(total_scores)/len(total_scores):.1f}")
        print(f"最高分: {max(total_scores):.1f}")
        print(f"最低分: {min(total_scores):.1f}")
        print(f"标准差: {(sum([(x-sum(total_scores)/len(total_scores))**2 for x in total_scores])/len(total_scores))**0.5:.1f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 批量测试失败: {e}")
        return False

def interactive_menu():
    """交互式菜单"""
    print_separator("普通话测评系统 - 训练和测试")
    
    print("🎙️  基于标准音频的普通话测评系统")
    print("\n📋 使用流程:")
    print("   1️⃣  首次使用: 先构建标准模板 (基于您的50篇标准音频)")
    print("   2️⃣  日常使用: 使用模板评测待测音频")
    print("   3️⃣  验证测试: 批量测试观察效果")
    
    while True:
        print("\n" + "="*50)
        print("请选择操作:")
        print("1. 构建标准评测模板 (首次使用必须)")
        print("2. 使用模板评测音频")
        print("3. 批量对比测试")
        print("4. 查看系统状态")
        print("5. 退出")
        
        try:
            choice = input("\n请输入选项 (1-5): ").strip()
            
            if choice == '1':
                success = step1_build_templates()
                if success:
                    print("\n✅ 模板构建成功! 现在可以进行音频评测了")
                else:
                    print("\n❌ 模板构建失败，请检查数据文件")
            
            elif choice == '2':
                success = step2_test_with_templates()
                if not success:
                    print("\n💡 提示: 请先运行选项1构建标准模板")
            
            elif choice == '3':
                success = step3_batch_comparison()
                if not success:
                    print("\n💡 提示: 请先运行选项1构建标准模板")
            
            elif choice == '4':
                show_system_status()
            
            elif choice == '5':
                print("\n👋 感谢使用普通话测评系统!")
                break
            
            else:
                print("❌ 无效选项，请重新选择")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已退出")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")

def show_system_status():
    """显示系统状态"""
    print_separator("系统状态检查")
    
    # 检查数据文件
    audio_dir = Path("分开")
    text_file = Path("50篇--朗读短文.docx(1).txt")
    templates_file = Path("standard_templates.json")
    
    print("📁 数据文件检查:")
    
    if audio_dir.exists():
        audio_files = list(audio_dir.glob("*.MP3"))
        print(f"   ✅ 音频目录存在: {len(audio_files)} 个MP3文件")
    else:
        print("   ❌ 音频目录不存在: 分开/")
    
    if text_file.exists():
        print(f"   ✅ 文本文件存在: {text_file}")
    else:
        print(f"   ❌ 文本文件不存在: {text_file}")
    
    if templates_file.exists():
        print(f"   ✅ 标准模板存在: {templates_file}")
        
        # 尝试加载模板查看详情
        try:
            import json
            with open(templates_file, 'r', encoding='utf-8') as f:
                templates = json.load(f)
            
            print("   📊 模板详情:")
            print(f"      声母模板: {len(templates.get('initials', {}))}")
            print(f"      韵母模板: {len(templates.get('finals', {}))}")
            print(f"      声调模板: {len(templates.get('tones', {}))}")
            print(f"      流畅性模板: {'是' if templates.get('fluency') else '否'}")
            
        except Exception as e:
            print(f"   ⚠️  模板文件可能损坏: {e}")
    else:
        print(f"   ❌ 标准模板不存在: {templates_file}")
        print("      💡 请先运行选项1构建标准模板")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
        
        if mode == 'build':
            step1_build_templates()
        elif mode == 'test':
            step2_test_with_templates()
        elif mode == 'batch':
            step3_batch_comparison()
        elif mode == 'status':
            show_system_status()
        else:
            print(f"❌ 未知模式: {mode}")
            print("可用模式: build, test, batch, status")
    else:
        # 交互式模式
        interactive_menu()

if __name__ == "__main__":
    main()
