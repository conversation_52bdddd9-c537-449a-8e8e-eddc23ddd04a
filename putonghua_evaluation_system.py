#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
普通话测评系统
基于50篇标准朗读短文的语音评测系统
评测维度：声母、韵母、声调、流畅性
"""

import os
import re
import numpy as np
import librosa
import soundfile as sf
from typing import Dict, List, Tuple, Optional
import jieba
import pypinyin
from pypinyin import pinyin, lazy_pinyin, Style
import json
from dataclasses import dataclass
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class EvaluationResult:
    """评测结果数据类"""
    initials_score: float  # 声母得分
    finals_score: float    # 韵母得分
    tones_score: float     # 声调得分
    fluency_score: float   # 流畅性得分
    overall_score: float   # 总分
    detailed_feedback: Dict[str, any]  # 详细反馈

class PutonghuaEvaluationSystem:
    """普通话测评系统主类"""
    
    def __init__(self, data_dir: str = "."):
        """
        初始化评测系统
        
        Args:
            data_dir: 数据目录路径
        """
        self.data_dir = Path(data_dir)
        self.audio_dir = self.data_dir / "分开"
        self.text_file = self.data_dir / "50篇--朗读短文.docx(1).txt"
        
        # 音频参数
        self.sample_rate = 16000
        self.hop_length = 512
        self.n_mfcc = 13
        
        # 加载文本数据
        self.texts = self._load_texts()
        
        # 初始化评测模型
        self._init_evaluation_models()
        
        logger.info("普通话测评系统初始化完成")
    
    def _load_texts(self) -> Dict[int, str]:
        """加载50篇文本数据"""
        texts = {}
        
        try:
            with open(self.text_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析文本，提取每篇短文
            pattern = r'2024普通话水平测试试卷\((\d+)\).*?三、朗读短文.*?\n\n(.*?)(?=\n\n2024普通话水平测试试卷|\n节选自|$)'
            matches = re.findall(pattern, content, re.DOTALL)
            
            for match in matches:
                text_id = int(match[0])
                text_content = match[1].strip()
                # 清理文本格式
                text_content = re.sub(r'　+', '', text_content)  # 移除全角空格
                text_content = re.sub(r'/+', '', text_content)   # 移除朗读标记
                text_content = re.sub(r'\s+', '', text_content)  # 移除多余空格
                texts[text_id] = text_content
            
            logger.info(f"成功加载 {len(texts)} 篇文本")
            return texts
            
        except Exception as e:
            logger.error(f"加载文本失败: {e}")
            return {}
    
    def _init_evaluation_models(self):
        """初始化各个评测模型"""
        # 声母韵母声调映射表
        self.initials_map = self._build_initials_map()
        self.finals_map = self._build_finals_map()
        self.tones_map = {1: '一声', 2: '二声', 3: '三声', 4: '四声', 0: '轻声'}
        
        logger.info("评测模型初始化完成")
    
    def _build_initials_map(self) -> Dict[str, str]:
        """构建声母映射表"""
        return {
            'b': 'b', 'p': 'p', 'm': 'm', 'f': 'f',
            'd': 'd', 't': 't', 'n': 'n', 'l': 'l',
            'g': 'g', 'k': 'k', 'h': 'h',
            'j': 'j', 'q': 'q', 'x': 'x',
            'zh': 'zh', 'ch': 'ch', 'sh': 'sh', 'r': 'r',
            'z': 'z', 'c': 'c', 's': 's',
            'y': 'y', 'w': 'w', '': '零声母'
        }
    
    def _build_finals_map(self) -> Dict[str, str]:
        """构建韵母映射表"""
        return {
            'a': 'a', 'o': 'o', 'e': 'e', 'i': 'i', 'u': 'u', 'v': 'ü',
            'ai': 'ai', 'ei': 'ei', 'ui': 'ui', 'ao': 'ao', 'ou': 'ou', 'iu': 'iu',
            'ie': 'ie', 've': 'üe', 'er': 'er',
            'an': 'an', 'en': 'en', 'in': 'in', 'un': 'un', 'vn': 'ün',
            'ang': 'ang', 'eng': 'eng', 'ing': 'ing', 'ong': 'ong'
        }
    
    def load_audio(self, audio_id: int) -> Tuple[np.ndarray, int]:
        """
        加载音频文件
        
        Args:
            audio_id: 音频编号 (1-50)
            
        Returns:
            音频数据和采样率
        """
        audio_path = self.audio_dir / f"{audio_id:02d}.MP3"
        
        try:
            # 使用librosa加载音频，自动转换为16kHz单声道
            audio, sr = librosa.load(audio_path, sr=self.sample_rate, mono=True)
            logger.info(f"成功加载音频 {audio_id}, 时长: {len(audio)/sr:.2f}秒")
            return audio, sr
            
        except Exception as e:
            logger.error(f"加载音频 {audio_id} 失败: {e}")
            return np.array([]), 0
    
    def extract_acoustic_features(self, audio: np.ndarray) -> Dict[str, np.ndarray]:
        """
        提取声学特征
        
        Args:
            audio: 音频数据
            
        Returns:
            特征字典
        """
        features = {}
        
        try:
            # MFCC特征 - 用于声母韵母识别
            mfcc = librosa.feature.mfcc(
                y=audio, sr=self.sample_rate, 
                n_mfcc=self.n_mfcc, hop_length=self.hop_length
            )
            features['mfcc'] = mfcc
            
            # 基频特征 - 用于声调识别
            f0, voiced_flag, voiced_probs = librosa.pyin(
                audio, fmin=librosa.note_to_hz('C2'), 
                fmax=librosa.note_to_hz('C7'), sr=self.sample_rate
            )
            features['f0'] = f0
            features['voiced_flag'] = voiced_flag
            
            # 频谱质心 - 用于音质评估
            spectral_centroids = librosa.feature.spectral_centroid(
                y=audio, sr=self.sample_rate, hop_length=self.hop_length
            )[0]
            features['spectral_centroid'] = spectral_centroids
            
            # 过零率 - 用于清浊音判断
            zcr = librosa.feature.zero_crossing_rate(
                audio, hop_length=self.hop_length
            )[0]
            features['zcr'] = zcr
            
            # 能量特征
            rms = librosa.feature.rms(
                y=audio, hop_length=self.hop_length
            )[0]
            features['rms'] = rms
            
            logger.info("声学特征提取完成")
            return features
            
        except Exception as e:
            logger.error(f"特征提取失败: {e}")
            return {}
    
    def get_phonetic_transcription(self, text: str) -> List[Dict[str, str]]:
        """
        获取文本的音素转写
        
        Args:
            text: 输入文本
            
        Returns:
            音素信息列表
        """
        phonetic_info = []
        
        try:
            # 使用pypinyin获取拼音信息
            pinyin_list = pinyin(text, style=Style.TONE3, heteronym=False)
            
            for char, py in zip(text, pinyin_list):
                if char.strip() and char not in '，。！？；：""''（）【】':
                    py_str = py[0] if py else ''
                    
                    # 解析声母、韵母、声调
                    initial, final, tone = self._parse_pinyin(py_str)
                    
                    phonetic_info.append({
                        'char': char,
                        'pinyin': py_str,
                        'initial': initial,
                        'final': final,
                        'tone': tone
                    })
            
            logger.info(f"音素转写完成，共 {len(phonetic_info)} 个音节")
            return phonetic_info
            
        except Exception as e:
            logger.error(f"音素转写失败: {e}")
            return []
    
    def _parse_pinyin(self, pinyin_str: str) -> Tuple[str, str, int]:
        """
        解析拼音字符串，提取声母、韵母、声调
        
        Args:
            pinyin_str: 拼音字符串 (如: zhang1)
            
        Returns:
            (声母, 韵母, 声调)
        """
        if not pinyin_str:
            return '', '', 0
        
        # 提取声调
        tone = 0
        if pinyin_str[-1].isdigit():
            tone = int(pinyin_str[-1])
            pinyin_str = pinyin_str[:-1]
        
        # 提取声母和韵母
        initial = ''
        final = pinyin_str
        
        # 声母列表（按长度排序，优先匹配长的）
        initials = ['zh', 'ch', 'sh', 'b', 'p', 'm', 'f', 'd', 't', 'n', 'l', 
                   'g', 'k', 'h', 'j', 'q', 'x', 'r', 'z', 'c', 's', 'y', 'w']
        
        for init in initials:
            if pinyin_str.startswith(init):
                initial = init
                final = pinyin_str[len(init):]
                break
        
        return initial, final, tone
