#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的文本解析
"""

from putonghua_hybrid_system import PutonghuaHybridSystem

def test_text_parsing():
    """测试文本解析"""
    print("🔍 测试修复后的文本解析...")
    
    try:
        # 初始化系统
        system = PutonghuaHybridSystem(load_standard_templates=False)
        
        # 获取解析的文本
        texts = system.texts
        
        print(f"✅ 成功解析 {len(texts)} 篇文本")
        
        # 显示前3篇的详细信息
        for i in range(1, min(4, len(texts) + 1)):
            if i in texts:
                text = texts[i]
                print(f"\n📄 文本 {i}:")
                print(f"   原始长度: {len(text)} 字符")
                print(f"   前150字符: {text[:150]}")
                
                # 检查标点符号保留情况
                punctuation_chars = '，。！？；：'
                punctuation_count = sum(1 for c in text if c in punctuation_chars)
                print(f"   保留的标点符号: {punctuation_count} 个")
                
                # 测试纯文本提取
                clean_text = system.get_text_for_evaluation(text)
                print(f"   纯文本长度: {len(clean_text)} 字符")
                print(f"   纯文本前100字符: {clean_text[:100]}")
                
                # 测试音素单元提取
                phonetic_units = system.get_phonetic_units(text)
                print(f"   音素单元数量: {len(phonetic_units)} 个")
                
                if phonetic_units:
                    print(f"   前5个音素单元:")
                    for j, unit in enumerate(phonetic_units[:5]):
                        print(f"     {j+1}. {unit.char} -> {unit.pinyin} ({unit.initial}-{unit.final}-{unit.tone})")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_specific_text():
    """测试特定文本片段"""
    print("\n🧪 测试特定文本片段...")
    
    # 模拟一段包含标点符号的文本
    test_text = "照北京的老规矩，春节差不多在腊月的初旬就开始了。"腊七腊八，冻死寒鸦"，这是一年里最冷的时候。"
    
    system = PutonghuaHybridSystem(load_standard_templates=False)
    
    print(f"原始文本: {test_text}")
    
    # 测试清理后的文本
    cleaned = system._clean_text(test_text)
    print(f"清理后: {cleaned}")
    
    # 测试纯文本
    pure_text = system.get_text_for_evaluation(cleaned)
    print(f"纯文本: {pure_text}")
    
    # 测试音素提取
    units = system.get_phonetic_units(cleaned)
    print(f"音素单元数量: {len(units)}")
    
    if units:
        print("前10个音素:")
        for i, unit in enumerate(units[:10]):
            print(f"  {i+1}. {unit.char} -> {unit.pinyin}")

def main():
    """主函数"""
    print("📝 测试文本解析修复")
    print("="*40)
    
    # 测试文本解析
    success1 = test_text_parsing()
    
    # 测试特定文本
    test_specific_text()
    
    if success1:
        print("\n✅ 文本解析测试通过!")
        print("💡 现在标点符号被正确保留，音素提取使用纯文本")
    else:
        print("\n❌ 测试失败")

if __name__ == "__main__":
    main()
