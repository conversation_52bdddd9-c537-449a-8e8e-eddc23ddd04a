#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文本分割是否正确
"""

from putonghua_hybrid_system import PutonghuaHybridSystem
import json

def test_text_parsing():
    """测试文本解析"""
    print("🔍 测试文本分割功能...")
    
    try:
        # 初始化系统
        system = PutonghuaHybridSystem(load_standard_templates=False)
        
        # 获取解析的文本
        texts = system.texts
        
        print(f"✅ 成功解析 {len(texts)} 篇文本")
        
        # 显示前5篇的详细信息
        for i in range(1, min(6, len(texts) + 1)):
            if i in texts:
                text = texts[i]
                print(f"\n📄 文本 {i}:")
                print(f"   长度: {len(text)} 字符")
                print(f"   前100字符: {text[:100]}...")
                print(f"   后50字符: ...{text[-50:]}")
                
                # 检查是否包含标点符号（应该被清理掉）
                punctuation_count = sum(1 for c in text if c in '，。！？；：""''（）【】//')
                print(f"   剩余标点符号: {punctuation_count} 个")
                
                # 检查字符类型分布
                chinese_chars = sum(1 for c in text if '\u4e00' <= c <= '\u9fff')
                print(f"   中文字符: {chinese_chars} 个")
        
        # 保存解析结果到文件供检查
        output_file = "parsed_texts.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(texts, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 解析结果已保存到: {output_file}")
        
        # 统计信息
        if texts:
            lengths = [len(text) for text in texts.values()]
            avg_length = sum(lengths) / len(lengths)
            min_length = min(lengths)
            max_length = max(lengths)
            
            print(f"\n📊 统计信息:")
            print(f"   平均长度: {avg_length:.0f} 字符")
            print(f"   最短文本: {min_length} 字符")
            print(f"   最长文本: {max_length} 字符")
            
            # 检查是否有异常短或长的文本
            for text_id, text in texts.items():
                if len(text) < 200:
                    print(f"   ⚠️  文本 {text_id} 可能过短: {len(text)} 字符")
                elif len(text) > 800:
                    print(f"   ⚠️  文本 {text_id} 可能过长: {len(text)} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def compare_with_original():
    """与原始文件对比"""
    print("\n🔍 与原始文件对比...")
    
    try:
        # 读取原始文件
        with open("50篇--朗读短文.docx(1).txt", 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        # 手动提取第一篇作为对比
        lines = original_content.split('\n')
        manual_text = ""
        in_first_article = False
        
        for line in lines:
            line = line.strip()
            
            if '2024普通话水平测试试卷(1)' in line:
                in_first_article = True
                continue
            elif '2024普通话水平测试试卷(2)' in line:
                break
            elif in_first_article and line.startswith('三、朗读短文'):
                continue
            elif in_first_article and line.startswith('节选自'):
                break
            elif in_first_article and line:
                # 移除全角空格
                clean_line = line.replace('　', '')
                if clean_line:
                    manual_text += clean_line
        
        # 清理手动提取的文本
        manual_text = manual_text.replace('/', '').replace('//', '')
        manual_text = ''.join(c for c in manual_text if c not in '，。！？；：""''（）【】')
        
        # 获取系统解析的第一篇
        system = PutonghuaHybridSystem(load_standard_templates=False)
        system_text = system.texts.get(1, "")
        
        print(f"手动提取长度: {len(manual_text)}")
        print(f"系统解析长度: {len(system_text)}")
        
        if manual_text == system_text:
            print("✅ 解析结果完全一致!")
        else:
            print("⚠️  解析结果有差异")
            print(f"手动提取前100字符: {manual_text[:100]}")
            print(f"系统解析前100字符: {system_text[:100]}")
            
            # 找出差异位置
            for i, (c1, c2) in enumerate(zip(manual_text, system_text)):
                if c1 != c2:
                    print(f"第一个差异在位置 {i}: '{c1}' vs '{c2}'")
                    break
        
        return True
        
    except Exception as e:
        print(f"❌ 对比失败: {e}")
        return False

def main():
    """主函数"""
    print("📝 普通话测评系统 - 文本解析测试")
    print("="*50)
    
    # 测试文本解析
    success1 = test_text_parsing()
    
    # 与原始文件对比
    success2 = compare_with_original()
    
    if success1 and success2:
        print("\n✅ 所有测试通过!")
        print("💡 建议: 检查 parsed_texts.json 文件确认解析结果")
    else:
        print("\n❌ 部分测试失败，请检查文本分割逻辑")

if __name__ == "__main__":
    main()
